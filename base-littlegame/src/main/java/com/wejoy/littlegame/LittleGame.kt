package com.wejoy.littlegame

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.text.TextUtils
import androidx.annotation.IntDef
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.interfaces.IAction
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.lib.api.ApiService
import com.wejoy.littlegame.LittleGame.addGameInfoToIntent
import com.wejoy.littlegame.LittleGame.subInitFromIntent
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.wespy.helper.dialog.DialogBuild
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wespy.component.suspend.SuspendInfo
import com.wespy.component.suspend.SuspendManager.doSuspend

/**
 * 主进程子进程均存在。
 */
object LittleGame {
    private const val TAG = "LittleGame"
    private const val GAME_INFO_INTENT_KEY = "_little_game_info_"
    private const val TEAM_INFO_INTENT_KEY = "_little_team_info_"
    private const val USE_CONN_KEY = "useConn"

    /**
     * 主进程队伍信息有更新时，对其进行更新。
     * 房间信息确认时，更新最后一次
     * 子进程在打开界面后，不再更新
     */
    @JvmStatic
    var gameInfo = LittleGameInfo()
        private set

    /**
     * 主进程中使用的队伍信息相关。不会自动清理，只会被覆盖
     */
    @JvmStatic
    var teamInfo: TeamInfo = TeamInfo()
        private set

    /**
     * 匹配时预期匹配的时间
     */
    @JvmStatic
    var teamMatchExpectedTime: Int = 0

    /**
     * 是否使用TCP服务
     */
    @JvmStatic
    var useConnector: Boolean = false

    @JvmStatic
    fun onSaveInstance(activity: Activity, outState: Bundle) {
        if (activity != ActivityTaskManager.getInstance().topActivity) {
            return
        }
        if (gameInfo.gameType != 0) {
            val parcel = Parcel.obtain()
            runCatching {
                gameInfo.writeToParcel(parcel, Parcelable.PARCELABLE_WRITE_RETURN_VALUE)
                val buf = parcel.marshall()
                outState.putByteArray(GAME_INFO_INTENT_KEY, buf)
            }
            parcel.recycle()
        }
        if (teamInfo.tid > 0 || teamInfo.rid > 0) {
            outState.putString(TEAM_INFO_INTENT_KEY, JsonUtil.toJsonString(teamInfo))
        }
    }


    @JvmStatic
    fun onRestoreInstance(savedInstanceState: Bundle?) {
        val state = savedInstanceState ?: return
        if (gameInfo.gameType == 0) {
            state.getByteArray(GAME_INFO_INTENT_KEY)?.let { buf ->
                if (buf.isNotEmpty()) {
                    val parcel = Parcel.obtain()
                    runCatching {
                        parcel.unmarshall(buf, 0, buf.size)
                        parcel.setDataPosition(0)
                        gameInfo = LittleGameInfo.CREATOR.createFromParcel(parcel)
                    }
                    parcel.recycle()
                }
            }
        }
        if (teamInfo.tid <= 0 && teamInfo.rid <= 0) {
            val s = state.getString(TEAM_INFO_INTENT_KEY)
            if (!TextUtils.isEmpty(s)) {
                JsonUtil.parseJson(s, TeamInfo::class.java)?.let { teamInfo = it }
            }
        }
    }

    /**
     * 子进程中
     * 在游戏 Activity 中调用来获取从主进程传递的游戏数据
     * 由主进程在 [addGameInfoToIntent] 传入
     */
    @JvmStatic
    fun subInitFromIntent(intent: Intent): Boolean {
        val gameInfo = intent.getParcelableExtra<LittleGameInfo>(GAME_INFO_INTENT_KEY)
        useConnector = intent.getBooleanExtra(USE_CONN_KEY, false)
        HLog.d(
            TAG,
            HLog.USR,
            "init gameInfo rid={} hash={}",
            gameInfo?.roomInfo?.rid,
            gameInfo.hashCode()
        )
        if (gameInfo?.roomInfo != null) {
            this.gameInfo = gameInfo
            return true
        }
        return false
    }

    /**
     * 语音房小游戏
     */
    @JvmStatic
    fun updateVoiceGameInfo(gameInfo: LittleGameInfo) {
        this.gameInfo = gameInfo
        HLog.d(TAG, HLog.USR, "update voice game info: {}", gameInfo.rid)
    }

    /**
     * 从语音房退出时，清除与语音房关联的小游戏信息
     * @param rid 语音房 rid
     */
    @JvmStatic
    fun clearVoiceGameInfo(rid: Int) {
        if (this.gameInfo.rid == rid) {
            HLog.d(TAG, HLog.USR, "clear voice game info: {}", rid)
            clear()
        }
    }

    @JvmStatic
    fun fromTeamInfoNoSeat(): LittleGameInfo {
        val gameInfo = LittleGameInfo()
        if (teamInfo.tid > 0) {
            gameInfo.gameType = teamInfo.game_type
            gameInfo.mode = teamInfo.mode
            gameInfo.betLevel = teamInfo.betLevel
            gameInfo.gameMode = teamInfo.gameMode
        }
        return gameInfo
    }

    @JvmStatic
    fun updateCocosGameInfo(
        gameType: Int,
        gameMode: Int,
        betLevel: Int,
        mode: Int,
        currencyType: Int,
        rid: Int,
        uids: List<Int>,
        cid: Int
    ) {
        val gameInfo = this.gameInfo
        gameInfo.gameType = gameType
        gameInfo.gameMode = gameMode
        gameInfo.betLevel = betLevel
        gameInfo.mode = mode
        gameInfo.currencyType = currencyType
        if (rid > 0) {
            val uidMap =
                LittleGame.gameInfo.roomInfo?.seatList?.associateBy { it.uid } ?: emptyMap()
            val roomInfo =
                LittleGameRoomInfo(rid, uids.map { LittleGameSeat(it, uidMap[it]?.seatNum ?: 0) })
            LittleGame.gameInfo.roomInfo = roomInfo
        }
        gameInfo.cid = cid
    }

    /**
     * 一般为进入游戏前，主进程使用，传递至子进程，由 [subInitFromIntent] 解析
     */
    @JvmStatic
    fun addGameInfoToIntent(intent: Intent, gameInfo: LittleGameInfo, useConnector: Boolean) {
        intent.putExtra(GAME_INFO_INTENT_KEY, gameInfo)
        intent.putExtra(USE_CONN_KEY, useConnector)
        this.useConnector = useConnector
    }

    fun clearGameInfo() {
        val rid = gameInfo.rid
        gameInfo = LittleGameInfo()
        HLog.d(TAG, HLog.USR, "clear game info, rid:{}", rid)
    }

    @JvmStatic
    fun clear() {
        gameInfo = LittleGameInfo()
        teamInfo = TeamInfo()
        HLog.d(TAG, HLog.USR, "clear hash:{}", teamInfo.hashCode())
    }

    /**
     * 匹配时更新队伍信息
     */
    @JvmStatic
    fun updateTeamInfo(teamInfo: TeamInfo) {
        gameInfo.gameType = teamInfo.gameType
        gameInfo.mode = teamInfo.mode
        gameInfo.gameMode = teamInfo.gameMode
        gameInfo.betLevel = teamInfo.betLevel

        gameInfo.followUid = 0
        gameInfo.beforeStartData = ""
        gameInfo.isRestore = false
        gameInfo.unityScene = 0
        gameInfo.horizontal = isHorizontal(teamInfo.gameType)

        gameInfo.roomInfo = LittleGameRoomInfo(
            teamInfo.rid,
            teamInfo.seats.map { LittleGameSeat(it.uid, it.seatNum) })
        this.teamInfo = teamInfo
        HLog.d(
            TAG, HLog.USR,
            "update team info tid:{}, rid:{},seats:{} teamHash: {}",
            teamInfo.tid, teamInfo.rid, teamInfo.seats, teamInfo.hashCode(),
        )
    }

    private fun isHorizontal(gameType: Int): Boolean {
        return ConfigHelper.getInstance().getGameConfig(gameType).isHorizontalScreen
    }


    @JvmStatic
    fun touchMatchSuspend(startTime: Long) {
        val suspendInfo = touchSuspend(SuspendInfo.TYPE_MATCHING)
        suspendInfo.startTimeInMill = startTime
    }

    @JvmStatic
    fun touchCreateSuspend() {
        touchSuspend(SuspendInfo.TYPE_CREATE_TEAM)
    }

    private fun touchSuspend(type: Int): SuspendInfo {
        val gameType: Int = teamInfo.gameType
        val mode: Int = teamInfo.mode
        val tid = teamInfo.tid
        val suspendInfo = SuspendInfo(gameType, type, tid)
        suspendInfo.action =
            object : IAction<Context> {

                private fun onCocosResOk(actionContext: Context, api: ILittleGameApi) {
                    if (type == SuspendInfo.TYPE_MATCHING) {
                        api.gotoIceBallMatchActivity(actionContext, gameType, false)
                    } else {
                        if (TeamInfo.isCreateTeam(mode)) {
                            // vip 房挂起，不跳转到 vip 房
                            val canSupportCocosMatch = !GameConfig.isJackarooVip(gameType, teamInfo.gameMode)
                            api.gotoIceBallCreateRoomActivity(
                                actionContext,
                                gameType,
                                ILittleGameApi.JumpCreateInfo(
                                    fromGameMain = false,
                                    needShowInviteDialog = false,
                                    canSupportCocosMatch = canSupportCocosMatch,
                                    TrackSource.PREPARE_FLOAT_WINDOW
                                )
                            )
                        } else if (TeamInfo.isMorePlayerMatch(mode)) {
                            api.gotoIceBallMatchPrepareActivity(
                                actionContext,
                                gameType,
                                false,
                                TrackSource.PREPARE_FLOAT_WINDOW
                            )
                        } else {
                            api.gotoCocosMainActivity(actionContext, gameType)
                        }
                    }
                }

                override fun onAction(actionContext: Context) {
                    val api = ApiService.of(ILittleGameApi::class.java)
                    api.checkLoadResWithDialog(actionContext, gameType, "") {
                        onCocosResOk(actionContext, api)
                    }
                }
            }
        suspendInfo.onExit =
            object : IAction<Context> {
                override fun onAction(actionContext: Context) {
                    DialogBuild.newBuilder(actionContext).setSingleBtn(false).setTitle(R.string.tip)
                        .setContent(ResUtil.getStr(R.string.common_text_exit_team_tips1))
                        .setDialogCallback(object : DialogBuild.DialogCallback {
                            override fun onClickSure() {
                                suspendInfo.exitDirect?.onAction("")
                            }

                            override fun onClickCancel() = Unit
                        }).show()
                }
            }
        suspendInfo.exitDirect = object : IAction<Any> {
            override fun onAction(actionContext: Any) {
                val api = ApiService.of(ILittleGameApi::class.java)
                val duration = System.currentTimeMillis() - suspendInfo.startTimeInMill
                val needCancelMatch = type == SuspendInfo.TYPE_MATCHING
                api.onSuspendReqCancelMatch(
                    tid, gameType, duration, needCancelMatch
                )
            }
        }
        doSuspend(suspendInfo)
        return suspendInfo
    }

    /**
     * cocos 匹配时，cocos 内更新 tid, rid 等信息，
     * 这里部分场景需要以这个内容做判断，单独设置到这里。
     */
    fun updateByCocosMatch(rid: Int, tid: Int) {
        val roomInfo = gameInfo.roomInfo ?: LittleGameRoomInfo()
        roomInfo.rid = rid
        gameInfo.roomInfo = roomInfo
        teamInfo.tid = tid
    }
}

const val GAME_SCENE_BL_MAIN = 1 shl 8
const val GAME_SCENE_BL_MATCH = 1 shl 9
const val GAME_SCENE_LITTLE_GAME_MAIN = 1 shl 10
const val GAME_SCENE_LITTLE_GAME_MATCH = 1 shl 11
const val GAME_SCENE_LITTLE_GAME_REPLAY = 1 shl 12
const val GAME_SCENE_VOICE_MAIN = 1 shl 13

@IntDef(
    GAME_SCENE_BL_MAIN, GAME_SCENE_BL_MATCH,
    GAME_SCENE_LITTLE_GAME_MAIN, GAME_SCENE_LITTLE_GAME_MATCH,
    GAME_SCENE_LITTLE_GAME_REPLAY, GAME_SCENE_VOICE_MAIN
)
@Retention(AnnotationRetention.SOURCE)
annotation class GameScene

const val EXCHANGE_TYPE_ENTER = 1 //进场不足
const val EXCHANGE_TYPE_MATCH = 2 //对局不足
const val EXCHANGE_TYPE_MAIN = 3 //大厅主动兑换

@IntDef(EXCHANGE_TYPE_ENTER, EXCHANGE_TYPE_MATCH, EXCHANGE_TYPE_MAIN)
@Retention(AnnotationRetention.SOURCE)
annotation class ChipExchangeType