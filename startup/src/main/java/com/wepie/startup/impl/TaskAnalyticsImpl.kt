package com.wepie.startup.impl

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import androidx.collection.MutableScatterMap
import androidx.collection.ScatterMap
import com.wepie.startup.InitializerCallback
import com.wepie.startup.InitializerCallbackProxy
import com.wepie.startup.InitializerManager
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

internal class DefaultTaskAnalytics(private val callback: InitializerManager.IErrorCallback) :
    ITaskAnalytics {

    internal var startFilterTime = 0L

    internal val startTime: Long = SystemClock.elapsedRealtime()
    internal var dispatchTime: Long = -1L

    internal val headTaskDataQueue: Queue<TaskData> = LinkedBlockingQueue()

    internal val finishTaskMap: MutableMap<Any, TaskData> = ConcurrentHashMap()

    private var timeout: TaskAsyncTimeout? = null

    private val callbackList: MutableList<InitializerCallbackProxy> by lazy {
        ArrayList()
    }

    override fun onTaskStart(task: InitializerTask) = Unit

    override fun onTaskFinish(task: InitializerTask) {
        val threadName = Thread.currentThread().name
        val data = TaskData(threadName, task.toString(), task.startTime, task.endTime)
        val tag = task.initializer.tag()
        finishTaskMap[tag] = data
        val iterator = callbackList.iterator()
        while (iterator.hasNext()) {
            val proxy = iterator.next()
            if (proxy.onTaskFinish(tag, data)) {
                iterator.remove()
            }
        }
        callback.onLog("onTaskFinish $task")
    }

    override fun onFilterStart(filterTag: Int) {
        startFilterTime = SystemClock.elapsedRealtime()
        this.timeout?.exit()
        val timeout = TaskAsyncTimeout(this, filterTag)
        timeout.timeout(5L, TimeUnit.SECONDS)
        timeout.enter()
        this.timeout = timeout
    }

    override fun onFilterEnd(filterTag: Int, filterBackground: Long) {
        val current = SystemClock.elapsedRealtime()
        timeout?.exit()
        addData("filter-$filterTag", startFilterTime, current)
    }

    override fun onDispatchStart() {
        dispatchTime = SystemClock.elapsedRealtime()
        addData("application", startTime, dispatchTime)
    }

    override fun onDispatchEnd() {
        addData("dispatchTask", dispatchTime, SystemClock.elapsedRealtime())
    }

    override fun onInitTaskEnd() {
        addData("initTask", dispatchTime, SystemClock.elapsedRealtime())
    }

    override fun onFinish() {
        addData("total", startTime, SystemClock.elapsedRealtime())
    }

    override fun addData(title: String, start: Long, end: Long) {
        headTaskDataQueue.add(TaskData("Head", title, start, end))
    }

    override fun checkTasks(map: ScatterMap<Any, InitializerTask>) = Unit
    override fun error(s: String) = Unit
    override fun errorLog(s: String) = Unit

    override fun errorLog(e: Exception) {
        callback.onCrash(e)
    }

    override fun setPrintFlags(vararg filterFlags: Int) = Unit

    override fun print(title: String) = Unit

    override fun addCallback(callback: InitializerCallback) {
        val proxy = InitializerCallbackProxy(callback)
        var isFinish = true
        callback.tags().forEach { tag ->
            val taskData = finishTaskMap[tag]
            if (taskData != null) {
                proxy.onTaskFinish(tag, taskData)
            } else {
                isFinish = false
            }
        }
        if (!isFinish) {
            callbackList.add(proxy)
        }
    }

    override fun removeCallback(callback: InitializerCallback) {
        val iterator = callbackList.iterator()
        while (iterator.hasNext()) {
            val proxy = iterator.next()
            if (proxy.real == callback) {
                iterator.remove()
                break
            }
        }
    }

    override fun onFilterTimeOut(task: InitializerTask, e: TimeoutException?) {
        val filterTag = timeout?.filterTag ?: 0
        timeout?.exit()
        val current = SystemClock.elapsedRealtime()
        val builder = StringBuilder()
        builder.append("[")
        headTaskDataQueue.forEach {
            builder.append(it).append(",")
        }
        builder.append(TaskData("Head", "filter-$filterTag", startFilterTime, current)).append(",")
        val taskTitle = task.toString()
        var duration: Long = 0
        finishTaskMap.forEach {
            val data = it.value
            builder.append(data).append(",")
            if (data.title == taskTitle) {
                duration = data.end - data.start
            }
        }
        if (duration <= 0) {
            if (task.startTime > 0) {
                val threadName = Thread.currentThread().name
                val data = TaskData(threadName, task.toString(), task.startTime, current)
                duration = current - startTime
                builder.append(data)
            }
        }
        builder.append("]")
        callback.onTimeOut(task.initializer, task.isDone, duration, builder.toString())
    }

    override fun filterTask(task: InitializerTask) {
        timeout?.filterTask(task)
        callback.onLog("wait $task")
    }
}

internal class DebugTaskAnalytics(
    private val context: Context,
    callback: InitializerManager.IErrorCallback
) : ITaskAnalytics {

    private val analytics = DefaultTaskAnalytics(callback)

    private var realStartTime = 0L
    private var printFlags: MutableList<Int> = mutableListOf()

    private val lifecycleCallback = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onCreate", time, time + 1)
        }

        override fun onActivityStarted(activity: Activity) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onStart", time, time + 1)
        }

        override fun onActivityResumed(activity: Activity) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onResume", time, time + 1)
        }

        override fun onActivityPaused(activity: Activity) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onPause", time, time + 1)
        }

        override fun onActivityStopped(activity: Activity) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onStop", time, time + 1)
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) = Unit

        override fun onActivityDestroyed(activity: Activity) {
            val time = SystemClock.elapsedRealtime()
            addData("${activity.javaClass.simpleName}-onDestroy", time, time + 1)
        }
    }

    init {
        val application = context as Application
        application.registerActivityLifecycleCallbacks(lifecycleCallback)
    }

    override fun onTaskStart(task: InitializerTask) {
        analytics.onTaskStart(task)
    }

    override fun onTaskFinish(task: InitializerTask) {
        analytics.onTaskFinish(task)
    }

    override fun onFilterStart(filterTag: Int) {
        analytics.onFilterStart(filterTag)
    }

    @Synchronized
    override fun onFilterEnd(filterTag: Int, filterBackground: Long) {
        analytics.onFilterEnd(filterTag, filterBackground)
        if (filterBackground >= 5) {
            Log.e(TAG, "onFilterEnd,filterTag:$filterTag background:$filterBackground")
        }
        if (printFlags.remove(filterTag)) {
            val tasks = MutableScatterMap<Any, TaskData>(analytics.finishTaskMap.size)
            tasks.putAll(analytics.finishTaskMap)
            Thread {
                val data = MutableScatterMap<String, MutableList<TaskData>>(tasks.size)
                tasks.forEachValue {
                    data.getOrPut(it.tag) { ArrayList() }.add(it)
                }
                printGantt("Start-$filterTag", analytics.headTaskDataQueue, data)
            }.start()
        }
    }

    override fun onDispatchEnd() {
        analytics.onDispatchEnd()
    }

    override fun onFinish() {
        analytics.onFinish()
        val application = context as Application
        application.unregisterActivityLifecycleCallbacks(lifecycleCallback)
    }

    override fun addData(title: String, start: Long, end: Long) {
        analytics.addData(title, start, end)
    }

    override fun error(s: String) {
        throw IllegalStateException(s)
    }

    override fun errorLog(s: String) {
        Log.e(TAG, "errorLog=${s}")
    }

    override fun errorLog(e: Exception) {
        Log.e(TAG, "errorLog=${Log.getStackTraceString(e)}")
    }

    override fun setPrintFlags(vararg filterFlags: Int) {
        analytics.setPrintFlags(*filterFlags)
        realStartTime = System.currentTimeMillis()
        printFlags = filterFlags.toMutableList()
    }

    override fun print(title: String) {
        val data = MutableScatterMap<String, MutableList<TaskData>>()
        analytics.finishTaskMap.values.forEach {
            val tag = it.tag
            data.getOrPut(tag) { ArrayList<TaskData>() }.add(it)
        }
        printGantt(title, analytics.headTaskDataQueue, data)
    }

    override fun addCallback(callback: InitializerCallback) {
        analytics.addCallback(callback)
    }

    override fun removeCallback(callback: InitializerCallback) {
        analytics.removeCallback(callback)
    }

    override fun onFilterTimeOut(task: InitializerTask, e: TimeoutException?) {
        throw Exception("filter task =$task", e)
    }

    override fun filterTask(task: InitializerTask) {
        analytics.filterTask(task)
    }

    override fun onDispatchStart() {
        analytics.onDispatchStart()
    }

    override fun onInitTaskEnd() {
        analytics.onInitTaskEnd()
    }

    override fun checkTasks(map: ScatterMap<Any, InitializerTask>) {
        val queue = ArrayDeque<InitializerTask>(map.size)
        val taskIndexHashMap = MutableScatterMap<InitializerTask, Int>()
        // 建立每个 task 的入度关系
        val taskChildMap = MutableScatterMap<Any, MutableList<InitializerTask>>(map.size)
        map.forEach { _, task ->
            val initializer = task.initializer
            val dependencies = initializer.dependencies()
            if (dependencies.isEmpty()) {
                queue.offer(task)
            } else {
                dependencies.forEach { tag ->
                    var children = taskChildMap[tag]
                    if (children == null) {
                        children = LinkedList()
                        taskChildMap[tag] = children
                        if (map[tag] == null) {
                            error("tag:$tag is not exist,$task dependence it")
                        }
                    }
                    children.add(map[initializer.tag()]!!)
                }
                taskIndexHashMap[task] = dependencies.size
            }
        }

        val result = LinkedList<InitializerTask>()
        // 使用 BFS 方法获得有向无环图的拓扑排序
        while (queue.isNotEmpty()) {
            val task = queue.pop()
            result.add(task)
            val children = taskChildMap[task.initializer.tag()]
            children?.forEach { child ->
                var index = taskIndexHashMap[child] ?: 0
                index -= 1
                if (index == 0) {
                    queue.offer(child)
                } else {
                    taskIndexHashMap[child] = index
                }
            }
        }

        checkResult(result, map)
    }

    @Throws(IllegalStateException::class)
    private fun checkResult(
        result: List<InitializerTask>,
        taskMap: ScatterMap<Any, InitializerTask>
    ) {
        if (taskMap.size != result.size) {
            val map = MutableScatterMap<Any, InitializerTask>()
            map.putAll(taskMap)
            result.forEach {
                map.remove(it.initializer.tag(), it)
            }
            val temp = ArrayList<InitializerTask>(map.size)
            map.forEach { _, task ->
                temp.add(task)
            }
            throw IllegalStateException("Ring appeared，Please check.temp is $temp")
        }

        result.forEach {
            val flag = it.initializer.flags() ushr 1
            it.initializer.dependencies().forEach { cls ->
                val task = taskMap[cls]!!
                val parentFlag = task.initializer.flags() ushr 1
                if (parentFlag > flag) {
                    throw IllegalStateException("$task can not be parent for $it")
                }
            }
        }
    }

    //打印甘特图
    private fun printGantt(
        ganttTitle: String, head: Queue<TaskData>, data: ScatterMap<String, MutableList<TaskData>>
    ) {
        val interval = realStartTime - analytics.startTime
        val builder = StringBuilder()
        builder.append("gantt\nDateFormat X\ntitle ").append("$ganttTitle\n")
        builder.append("section head\n")
        head.forEach {
            builder.append("${it.title}-${it.end - it.start}:${it.start + interval},${it.end + interval}\n")
        }
        data.forEach { key, value ->
            builder.append("section ${key}\n")
            val list = ArrayList(value)
            list.sortWith { o1, o2 -> ((o1?.start ?: 0) - (o2?.start ?: 0)).toInt() }
            list.forEach { d ->
                builder.append("${d.title}-${d.end - d.start}")
                builder.append(":${d.start + interval},${d.end + interval}\n")
            }
        }
        Log.e(TAG, "printGantt==${android.os.Process.myPid()}\n$builder")
    }

    companion object {
        internal const val TAG = "TaskAnalysis"
    }
}