package com.huiwan.littlegame.cocos

import android.annotation.SuppressLint
import android.content.Context
import android.content.MutableContextWrapper
import android.net.Uri
import android.os.Looper
import android.util.AttributeSet
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.webkit.WebViewAssetLoader
import androidx.webkit.WebViewAssetLoader.InternalStoragePathHandler
import androidx.webkit.WebViewClientCompat
import com.github.lzyzsd.jsbridge.BridgeHandler
import com.github.lzyzsd.jsbridge.BridgeOriginWebView
import com.github.lzyzsd.jsbridge.BridgeUtil
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.github.lzyzsd.jsbridge.ICallback
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.IWebProxyApi
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.store.PrefUtil
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGameInfo
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import com.wepie.webview.intercept.WebAutoCloseInputStream
import com.wepie.webview.intercept.WebRequest
import com.wepie.webview.intercept.WebViewCacheInterceptorInst.interceptRequest
import java.io.File
import java.lang.ref.SoftReference
import java.net.URLDecoder
import java.net.URLEncoder
import java.util.concurrent.ConcurrentLinkedDeque

class CocosWebView : FrameLayout {
    var gameInfo: LittleGameInfo = LittleGameInfo()
    var launchInfo = CocosLaunchInfo()
    private var isWebViewDestroy = false
    private val webView: BridgeOriginWebView? = try {
        BridgeOriginWebView(context.applicationContext).apply {
            openMessageQueue(WebApi::class.impl().isOpenMessageQueue)
            val needClearCache = PrefUtil.getInstance().getBoolean(PrefUtil.CLEAR_WEB_CACHE, false)
            if (needClearCache) {
                PrefUtil.getInstance().setBoolean(PrefUtil.CLEAR_WEB_CACHE, false)
                clearCache(true)
                HLog.e(TAG, HLog.USR, "clear webview disk cache")
            }
        }
    } catch (e: Exception) {
        null
    }
    var path = ""
        set(path) {
            field = path + RES_NAME + File.separator
        }
    var startTimeMs = System.currentTimeMillis()

    var scene = SCENE_LITTLE_GAME
    var safeTop = 0
    var safeBottom = 0

    private var isLoading = true

    private var initTime: Long = 0

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    // loading期间已经完成了的url请求
    private val finishedUrlRequestsWhileLoading = ConcurrentLinkedDeque<String>()
    private var startVisitUrlMillis = 0L
    private var preloadAssetEnabled = ApiService.of(ILittleGameApi::class.java).getPreloadCocosAssetEnabled()

    init {
        if (webView != null) {
            addView(webView)
            initWebViewInternal(webView)
        }
    }

    @SuppressLint("AddJavascriptInterface", "SetJavaScriptEnabled")
    private fun initWebViewInternal(webView: BridgeOriginWebView) {
        initTime = System.currentTimeMillis()
        ApiService.of(WebApi::class.java)
        val webSettings = webView.settings
        webSettings.allowFileAccess = true
        webSettings.allowContentAccess = true
        webSettings.domStorageEnabled = true
        webSettings.cacheMode = if(ApiService.of(ILittleGameApi::class.java).getCocosWebViewCacheEnabled()) {
            WebSettings.LOAD_CACHE_ELSE_NETWORK
        } else {
            WebSettings.LOAD_NO_CACHE
        }
        webSettings.javaScriptEnabled = true
        //Webview在安卓5.0之前默认允许其加载混合网络协议内容 * 在安卓5.0之后，WebView默认不允许加载http与https混合内容，需要设置webview允许其加载混合网络协议内容
        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        //chrome debug
        WebView.setWebContentsDebuggingEnabled(LibBaseUtil.buildDebug())

        // https://appassets.androidplatform.net/games/game_2007/cocos/cocos_unzip/cocos_game_dev/web_unpack_2007/web-mobile/index.html
        // publicDir: /data/data/com.wejoy.weplay.jp/files/cocos_game_dev
        val assetLoader = WebViewAssetLoader.Builder()
            .addPathHandler("/", InternalStoragePathHandler(context, context.filesDir))
            .build()
        webView.webViewClient = object : WebViewClientCompat() {
            override fun shouldInterceptRequest(
                view: WebView, request: WebResourceRequest
            ): WebResourceResponse? {
                var rsp: WebResourceResponse?
                if (preloadAssetEnabled) {
                    // 尝试从预加载过的资源中获取
                    rsp = CocosWebViewPreloadAssetHelper.tryReadResponseFromPreload(request.url.toString())
                    if (rsp != null) {
                        // 预加载结果命中，如果此时还处于loading状态，将url更新到list里，加载结束后保存到本地
                        if (isLoading) {
                            finishedUrlRequestsWhileLoading.add(request.url.toString())
                        }
                        return rsp
                    }
                }

                // 未执行与加载或预加载结果未命中，走正常拦截加载
                rsp = interceptAsset(assetLoader, request)
                if (rsp != null) {
                    // 拦截命中，如果此时还处于loading状态，将url更新到list里，加载结束后保存到本地
                    if (preloadAssetEnabled && isLoading) {
                        finishedUrlRequestsWhileLoading.add(request.url.toString())
                    }
                    return rsp
                }
                rsp = CocosAssetBundleResLoader.interceptWebRequest(request)
                if (rsp != null) {
                    return rsp
                }
                if ("GET" != request.method) {
                    return null
                }
                val proxyRequest = transformProxyRequest(request) ?: return null
                val webProxyApi = ApiService.of(IWebProxyApi::class.java)
                if (webProxyApi != null && webProxyApi.shouldProxy(Uri.parse(proxyRequest.url)) == true) {
                    val response = interceptRequest(proxyRequest)
                    if (response != null) {
                        return response.response
                    }
                }
                return null
            }

            override fun onReceivedHttpError(
                view: WebView, request: WebResourceRequest, errorResponse: WebResourceResponse
            ) {
                if (!TextUtil.isEmpty(errorResponse.reasonPhrase)) {
                    super.onReceivedHttpError(view, request, errorResponse)
                }
            }
        }
        webView.registerAndroidJs(WESPY, BridgeUtil.WESPY_JS)
        CocosBridgeInterface.registerCocos(this)
    }

    /**
     * cocos资源加载wpjk协议
     * cocos游戏资源代理协议统一为wpjk://xxx?game_type=1041&res_type=1&ori_scheme=https格式
     */
    private fun transformProxyRequest(originRequest: WebResourceRequest): WebRequest? {
        if (WPJK_SCHEME == originRequest.url.scheme?.trim(' ')) {
            val originScheme = originRequest.url.getQueryParameter(ORIGIN_SCHEME)
            if (originScheme == null) {
                HLog.d(TAG, HLog.USR, "originScheme is null url: ${originRequest.url}")
                return null
            }

            val url = transformRequestUri(originRequest.url, originScheme).toString()
            val webRequest = WebRequest(
                url,
                originRequest.method,
                originScheme,
                true,
                originRequest.requestHeaders
            )
            return webRequest
        }
        return WebRequest(originRequest)
    }

    private fun transformRequestUri(originalUri: Uri, originalScheme: String): Uri {
        val params = originalUri.query?.split("&")?.associate {
            val (key, value) = it.split("=")
            key to URLDecoder.decode(value, "UTF-8")
        }?.toMutableMap()
        if (params != null) {
            params.keys.removeAll { filterParams(it) }
            val newQuery = formNewQuery(params)
            return originalUri.buildUpon().scheme(originalScheme).query(newQuery).build()

        }

        return originalUri.buildUpon().scheme(originalScheme).build()
    }

    private fun formNewQuery(params: Map<String, String>): String {
        return params.map { (key, value) ->
            val encodedKey = URLEncoder.encode(key, "UTF-8")
            val encodedValue = URLEncoder.encode(value, "UTF-8")
            "$encodedKey=$encodedValue"
        }.joinToString("&")
    }

    private fun interceptAsset(
        assetLoader: WebViewAssetLoader, request: WebResourceRequest
    ): WebResourceResponse? {
        val rsp = assetLoader.shouldInterceptRequest(request.url) ?: return null
        try {
            rsp.data = WebAutoCloseInputStream.create(request.url.toString(), rsp.data)
            return rsp
        } catch (e: Exception) {
            return assetLoader.shouldInterceptRequest(request.url)
        }
    }

    fun visitUrl(url: String) {
        // Assets are hosted under http(s)://appassets.androidplatform.net/assets/... .
        // If the application's assets are in the "main/assets" folder this will read the file
        // from "main/assets/www/index.html" and load it as if it were hosted on:
        // https://appassets.androidplatform.net/assets/www/index.html
        webView?.let {
            isLoading = true
            var realUrl = url
            if (LibBaseUtil.buildDebug()) {
                val debugHost = PrefUtil.getInstance().getString(PrefUtil.COCOS_DEBUG_HOST, "")
                val debugSwitch =
                    PrefUtil.getInstance().getBoolean(PrefUtil.COCOS_DEBUG_SWITCH, true)
                if (debugSwitch && debugHost.isNotEmpty()) {
                    ToastUtil.debugShow("Cocos Target: $debugHost")
                    realUrl = debugHost
                }
            }
            // 目前设计的是在启动activity前到loadUrl这之间做预加载，执行loadUrl后取消预加载的协程，走web自己的加载
            // 但实际上继续加载也行，相当于是预加载协程和正常加载线程同时进行，要考虑的情况稍微多一点
            CocosWebViewPreloadAssetHelper.cancelPreloadCoroutineIfStillRunning()
            startVisitUrlMillis = System.currentTimeMillis()
            it.loadUrl(realUrl)
        }
    }

    fun destroy() {
        isLoading = false
        CocosWebViewPreloadAssetHelper.release()
        if (isWebViewDestroy) {
            return
        }
        CocosBridgeInterface.release()
        webView?.apply {
            stopLoading()
            clearMessage()
            destroy()
            <EMAIL>(this)
            isWebViewDestroy = true
        }
    }

    fun callHandler(handlerName: String, data: String?, callBack: CallBackFunction?) {
        webView?.callHandler(handlerName, data, callBack)
    }

    fun supportHandler(handlerName: String, callback: ICallback<Boolean>) {
        webView?.supportHandler(handlerName, callback) ?: callback.onCallBack(false)
    }

    fun registerHandler(handlerName: String, handler: BridgeHandler?) {
        webView?.registerHandler(handlerName, handler)
    }

    fun setIsLoading(value: Boolean) {
        if (isLoading && !value) {
            isLoading = false
            HLog.d(TAG, HLog.USR, "from loadUrl to hideLoading, totally take ${System.currentTimeMillis() - startVisitUrlMillis}")
            CocosWebViewPreloadAssetHelper.updateLocalFile(finishedUrlRequestsWhileLoading.toList())
        } else {
            isLoading = value
        }
    }

    fun getIsLoading() = isLoading

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (isWebViewDestroy) {
            val e = IllegalStateException("can not add view after destroy")
            if (LibBaseUtil.buildDebug()) {
                throw e
            } else {
                FLog.e(e)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        destroy()
    }

    override fun toString(): String {
        return "CocosWebView@${hashCode()}"
    }

    companion object {
        private const val TAG = "CocosWebView"
        private const val WESPY = "Wespy"

        private var map: MutableMap<Long, CocosWebView> = mutableMapOf()

        private fun createCocosWebView(context: Context): CocosWebView {
            return CocosWebView(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ScreenUtil.getScreenWidth(),
                    ScreenUtil.getScreenHeight()
                )
            }
        }

        @JvmStatic
        fun preload(context: Context) {
            if (map.isNotEmpty()) {
                return
            }
            val reference = SoftReference(context)
            Looper.myQueue().addIdleHandler {
                if (map.isNotEmpty()) {
                    return@addIdleHandler false
                }
                val ctx = MutableContextWrapper(reference.get() ?: LibBaseUtil.getApplication())
                map[0L] = createCocosWebView(ctx)
                ctx.baseContext = LibBaseUtil.getApplication()
                HLog.d(TAG, HLog.USR, "preloadWebView: {}", map)
                return@addIdleHandler false
            }
        }

        @JvmStatic
        fun getWebViewFromCache(context: Context, jsbConfig: CocosWebViewJsbConfig): CocosWebView {
            val iterator = map.iterator()
            CocosBridgeInterface.jsbConfig = jsbConfig
            while (iterator.hasNext()) {
                val entity = iterator.next()
                iterator.remove()
                HLog.d(TAG, HLog.USR, "getWebViewFromCache-cache: @${entity.value.hashCode()}")
                return entity.value.also {
                    val ctx = it.context
                    if (ctx is MutableContextWrapper) {
                        ctx.baseContext = context
                    }
                }
            }
            val ctx = MutableContextWrapper(context)
            val webView = createCocosWebView(ctx).also {
                ctx.baseContext = context
            }
            HLog.d(TAG, HLog.USR, "getWebViewFromCache-new: @${webView.hashCode()}")
            return webView
        }


        const val WPJK_SCHEME = "wpjk" //cocos游戏资源代理协议
        const val ORIGIN_SCHEME = "ori_scheme__"
        const val GAME_TYPE = "game_type"
        const val RES_TYPE = "res_type__"
        const val ITEM_ID = "item_id__"
        const val DOUBLE_DASH = "__" //双端和cocos约定凡是以双下划线为后缀的参数为通信专用，要移除

        fun filterParams(param: String): Boolean {
            return param.endsWith(DOUBLE_DASH)
        }
    }
}