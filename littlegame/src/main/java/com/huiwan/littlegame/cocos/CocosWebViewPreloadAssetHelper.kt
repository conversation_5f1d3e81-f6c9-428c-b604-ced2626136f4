package com.huiwan.littlegame.cocos

import android.content.Context
import android.net.Uri
import android.webkit.WebResourceResponse
import androidx.webkit.WebViewAssetLoader
import com.huiwan.lib.api.ApiService
import com.huiwan.store.file.FileConfig
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.liblog.main.HLog
import com.wepie.webview.intercept.WebAutoCloseInputStream
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern
import kotlin.math.min

object CocosWebViewPreloadAssetHelper {

    //////////////////// 预加载相关
    private const val TAG = "CocosWebViewPreloadAssetHelper"
    // 文件目录
    private const val COCOS_PRELOAD_URLS_FILE_DIR = "cocos-preload-necessary-urls"
    // 预加载上限，一方面防止文件过大，另一方面是反正多了也预加载不过来，ludo撑死就预加载了80多条url
    private const val MAX_PRELOAD_URL_COUNT = 120
    private var curGameType = -1
    private var responseMap: ConcurrentHashMap<String, WebResourceResponse>? = null
    private var preloadJob: Job? = null
    // url写入txt时顺便记录下来，下次可以直接尝试从里面拿而不用每次都走IO
    private val urlRecordMap = HashMap<Int, List<Pair<String, Int>>>()

    /**
     * 预加载指定游戏对应的部分url，只用gameType进行区分
     * 在startActivity前调用，在loadUrl前取消协程，这之间的空档可以用来预加载资源
     */
    fun preloadAsset(context: Context, gameType: Int) {
        if (!ApiService.of(ILittleGameApi::class.java).getPreloadCocosAssetEnabled()) {
            return
        }
        // 当前已经有一个相同gameType的协程在跑，可以直接return
        if (curGameType == gameType && preloadJob?.isActive == true) {
            return
        }
        release()
        val applicationContext = context.applicationContext
        // 直接起一个协程，读出url后开始加载，该url在存入的时候已经按照请求次数进行排序了
        preloadJob = CoroutineScope(Dispatchers.IO).launch {
            val webViewAssetLoader = WebViewAssetLoader.Builder()
                .addPathHandler("/", WebViewAssetLoader.InternalStoragePathHandler(applicationContext, applicationContext.filesDir))
                .build()
            curGameType = gameType
            responseMap = ConcurrentHashMap()
            // 以txt中的文件为准，若读出来是空的，则只预加载初始的url，就是网页本身的那个html文件
            readFromTxtAsPairList(generatePreloadUrlFilePath()).ifEmpty {
                listOf(wrapUrl(getUnpackDir(gameType, true)) to 1)
            }.forEach {
                if (!isActive) {
                    // 协程取消
                    return@launch
                }
                interceptAsset(webViewAssetLoader, Uri.parse(it.first))?.let { response ->
                    responseMap?.put(it.first, response)
                }
            }
            // 协程自然结束，预加载完毕
        }
    }

    fun release() {
        responseMap = null
        cancelPreloadCoroutineIfStillRunning()
    }

    fun updateLocalFile(list: List<String>) {
        if (ApiService.of(ILittleGameApi::class.java).getPreloadCocosAssetEnabled()) {
            release()
            CoroutineScope(Dispatchers.IO).launch(Dispatchers.IO) {
                if (curGameType != -1) {
                    mergeToTxt(generatePreloadUrlFilePath(), list)
                    curGameType = -1
                }
            }
        }
    }

    fun cancelPreloadCoroutineIfStillRunning() {
        preloadJob?.cancel()
        preloadJob = null
    }

    fun tryReadResponseFromPreload(url: String): WebResourceResponse? {
        return responseMap?.remove(url)
    }

    private fun interceptAsset(
        assetLoader: WebViewAssetLoader, url: Uri
    ): WebResourceResponse? {
        val rsp = assetLoader.shouldInterceptRequest(url) ?: return null
        try {
            rsp.data = WebAutoCloseInputStream.create(url.toString(), rsp.data)
            return rsp
        } catch (e: Exception) {
            return assetLoader.shouldInterceptRequest(url)
        }
    }

    /**
     * 按照拦截命中次数降序排列后按照  [url][请求次数]  的格式存入本地文件
     */
    private fun overwriteListPairToTxt(path: String, content: List<Pair<String, Int>>) {
        if (content.isEmpty()) {
            return
        }
        // 写入的url数量不超过设定上限
        val subList = content.subList(0, min(content.size, MAX_PRELOAD_URL_COUNT))
        subList.sortedByDescending {
            it.second
        }.also {
            urlRecordMap.put(curGameType, it)
        }.map {
            "[${it.first}][${it.second}]"
        }.let {
            overwriteToTxt(path, it)
        }
    }

    private fun overwriteToTxt(path: String, content: List<String>) {
        if (content.isEmpty()) {
            return
        }
        try {
            val file = File(path)
            file.parentFile?.mkdirs()
            file.writeText("")
            file.bufferedWriter().use { writer ->
                content.forEachIndexed { index, line ->
                    writer.append(line)
                    if (index < content.size - 1) {
                        writer.newLine()
                    }
                }
            }
        } catch (e: Exception) {
            HLog.d(TAG, e.message)
        }
    }

    /**
     * @param forceIO 强制通过IO获取，如果为false则尝试从上一次的记录中获取，获取不到再走IO
     */
    private fun readFromTxtAsPairList(path: String, forceIO: Boolean = false): List<Pair<String, Int>> {
        return if (forceIO) {
            readFromTxtAsList(path).mapNotNull {
                parseLineToPair(it)
            }
        } else {
            urlRecordMap[curGameType]?.let {
                return it
            }
            readFromTxtAsList(path).mapNotNull {
                parseLineToPair(it)
            }.also {
                urlRecordMap[curGameType] = it
            }
        }
    }

    private fun readFromTxtAsList(path: String): List<String> {
        return try {
            File(path).takeIf { it.exists() }?.readLines() ?: emptyList()
        } catch (e: Exception) {
            HLog.d(TAG, e.message)
            emptyList()
        }
    }

    /**
     * 新旧数据合并，注意新数据需要去重：
     * 1. 新数据有 + 旧数据有   => 请求次数+1后写入
     * 2. 新数据有 + 旧数据没有  => 写入，请求次数=1
     * 3. 新数据没有 + 旧数据有  => 移除
     */
    private fun mergeToTxt(path: String, content: List<String>) {
        val oldDataMap = readFromTxtAsPairList(path).toMap()
        val newResultMap = oldDataMap.toMutableMap()
        // 对新数据去重后再处理
        val distinctNewUrls = content.distinct()
        // 处理新数据
        distinctNewUrls.forEach { newUrl ->
            newResultMap[newUrl] = oldDataMap.getOrDefault(newUrl, 0) + 1
        }
        // 移除旧数据中有但新数据中没有的项
        val newUrlSet = distinctNewUrls.toSet()
        oldDataMap.keys.filter { it !in newUrlSet }.forEach {
            newResultMap.remove(it)
        }
        overwriteListPairToTxt(path, newResultMap.toList())
    }

    /**
     * 获取指定gameType所需的预加载内容的文件路径
     */
    private fun generatePreloadUrlFilePath(gameType: Int = curGameType): String {
        return FileConfig.getExtraFolderPath() +
                COCOS_PRELOAD_URLS_FILE_DIR +
                File.separator +
                "preload-url-${gameType}.txt"
    }

    // 将 [url][5] 格式的字符串转化为pair
    private fun parseLineToPair(line: String): Pair<String, Int>? {
        val pattern = Pattern.compile("""\[(.*?)]\[(\d+)]""")
        val matcher = pattern.matcher(line)
        return if (matcher.matches()) {
            Pair(matcher.group(1)!!, matcher.group(2)!!.toInt())
        } else {
            null
        }
    }
}